#pragma once

#include <CoreMinimal.h>

#include "IChatRequestHandler.h"
#include "DruidsSageChatRequest_v2.h"

class ChatRequestHandler_V2 : public IChatRequestHandler
{
public:
    virtual bool IsNoActiveRequest() const override;
    virtual void StopAndCleanupRequest(TArray<UIDruidsSageChatItem*> ChatItems) override;
    virtual void SetupAndSendRequest(TArray<UIDruidsSageChatItem*> ChatItems,
                                     UIDruidsSageChatItem* AssistantMessage, const FString& Context) override;

private:
    TWeakObjectPtr<UDruidsSageChatRequest_v2> RequestReference;
	TSharedPtr<ISageExtensionDelegator> ExtensionDelegator;
};