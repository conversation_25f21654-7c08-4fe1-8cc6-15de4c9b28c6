#include "ChatRequestHandler_V2.h"
#include "DruidsSageMessagingHandler.h"
#include "DruidsSageHelper.h"
#include "IDruidsSageChatItem.h"
#include "SageExtensionDelegator.h"

bool ChatRequestHandler_V2::IsNoActiveRequest() const
{
    return !RequestReference.IsValid() || !UDruidsSageTaskStatus::IsTaskActive(RequestReference.Get());
}

void ChatRequestHandler_V2::StopAndCleanupRequest(TArray<UIDruidsSageChatItem*> ChatItems)
{
    if (RequestReference.IsValid())
    {
        // Find the last assistant message to get its messaging handler
        for (UIDruidsSageChatItem* ChatItem : ChatItems)
        {
            if (ChatItem)
            {
                if (ChatItem->GetMessageRoleCpp() == EDruidsSageChatRole::Assistant && ChatItem->GetMessagingHandlerCpp().IsValid())
                {
                    // Unbind all delegates
                    ChatItem->GetMessagingHandlerCpp()->OnMessageRequestSent.Unbind();
                    ChatItem->GetMessagingHandlerCpp()->OnMessageRequestFailed.Unbind();
                    ChatItem->GetMessagingHandlerCpp()->OnMessageContentUpdated.Unbind();
                    ChatItem->GetMessagingHandlerCpp()->OnMessageResponseUpdated.Unbind();

                    RequestReference->ProgressStarted.RemoveAll(ChatItem->GetMessagingHandlerCpp().Get());
                    RequestReference->ProgressUpdated.RemoveAll(ChatItem->GetMessagingHandlerCpp().Get());
                    RequestReference->ProcessCompleted.RemoveAll(ChatItem->GetMessagingHandlerCpp().Get());
                    RequestReference->ErrorReceived.RemoveAll(ChatItem->GetMessagingHandlerCpp().Get());
                    RequestReference->RequestFailed.RemoveAll(ChatItem->GetMessagingHandlerCpp().Get());
                    RequestReference->RequestSent.RemoveAll(ChatItem->GetMessagingHandlerCpp().Get());
                }
            }
        }
        
        RequestReference->StopDruidsSageTask();
        RequestReference.Reset();
    }
}

void ChatRequestHandler_V2::SetupAndSendRequest(TArray<UIDruidsSageChatItem*> ChatItems,
                                                 UIDruidsSageChatItem* AssistantMessage, const FString& Context)
{
    if (!AssistantMessage || !AssistantMessage->GetMessagingHandlerCpp().IsValid())
    {
        return;
    }

    TArray<FDruidsSageChatMessage> ChatHistory;
	for (UIDruidsSageChatItem* Item : ChatItems)
    {
        if (Item)
        {
            FString MessageRoleText = UDruidsSageHelper::RoleToName(Item->GetMessageRoleCpp()).ToString();

	        FDruidsSageChatMessage ChatMessage;
	        Item->FillInDruidsMessageCpp(ChatMessage);

            // Add UserFocusContext to user messages
            if (Item->GetMessageRoleCpp() == EDruidsSageChatRole::User && !Context.IsEmpty())
            {
                ChatMessage.SetUserFocusContext(Context);
            }

            ChatHistory.Add(ChatMessage);
        }
    }

    ExtensionDelegator = MakeShared<FSageExtensionDelegator>();
    RequestReference = UDruidsSageChatRequest_v2::EditorTask(ChatHistory, ExtensionDelegator, Context);

    RequestReference->ProgressStarted.AddDynamic(
        AssistantMessage->GetMessagingHandlerCpp().Get(),
        &UDruidsSageMessagingHandler::ProcessUpdated);

    RequestReference->ProgressUpdated.AddDynamic(
        AssistantMessage->GetMessagingHandlerCpp().Get(),
        &UDruidsSageMessagingHandler::ProcessUpdated);

    RequestReference->ProcessCompleted.AddDynamic(
        AssistantMessage->GetMessagingHandlerCpp().Get(),
        &UDruidsSageMessagingHandler::ProcessCompleted);

    RequestReference->ErrorReceived.AddDynamic(
        AssistantMessage->GetMessagingHandlerCpp().Get(),
        &UDruidsSageMessagingHandler::ProcessCompleted);

    RequestReference->RequestFailed.AddDynamic(
        AssistantMessage->GetMessagingHandlerCpp().Get(),
        &UDruidsSageMessagingHandler::RequestFailed);

    RequestReference->RequestSent.AddDynamic(
        AssistantMessage->GetMessagingHandlerCpp().Get(),
        &UDruidsSageMessagingHandler::RequestSent);

    RequestReference->Activate();
}