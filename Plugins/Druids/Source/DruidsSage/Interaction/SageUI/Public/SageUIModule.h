#pragma once

#include "CoreMinimal.h"
#include "Modules/ModuleManager.h"

// Forward declarations
class UChatWidgetOverrides;

class FSageUIModule : public IModuleInterface
{
public:
    virtual void StartupModule() override;
    virtual void ShutdownModule() override;

    /**
     * Gets the ChatWidgetOverrides instance for use by other modules.
     * @return The ChatWidgetOverrides instance, or nullptr if not loaded
     */
    UChatWidgetOverrides* GetChatWidgetOverrides();

private:
    /**
     * Loads the ChatWidgetOverrides from the hard-coded plugin content location.
     * @return The loaded ChatWidgetOverrides instance, or nullptr if not found or failed to load
     */
    UChatWidgetOverrides* LoadChatWidgetOverrides();

    /**
     * Cached instance of the ChatWidgetOverrides loaded from Blueprint
     */
    TObjectPtr<UChatWidgetOverrides> CachedChatWidgetOverrides;
};
