#pragma once

#include "CoreMinimal.h"
#include "Components/RichTextBlock.h"
#include "Engine/Engine.h"

#include "MarkdownRichTextBlock.generated.h"

UCLASS(BlueprintType, Blueprintable)
class SAGEUI_API UMarkdownRichTextBlock : public URichTextBlock
{
	GENERATED_BODY()

public:
	UMarkdownRichTextBlock(const FObjectInitializer& ObjectInitializer);

	// UUserWidget interface
	virtual void SynchronizeProperties() override;
	// End of UUserWidget interface

	// Configuration properties (using different names to avoid shadowing base class properties)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Markdown")
	bool bMarkdownAutoWrapText = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Markdown")
	float MarkdownWrapTextAt = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Markdown")
	FMargin MarkdownTextMargin = FMargin(0.0f);

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Markdown")
	float MarkdownLineHeightPercentage = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Markdown")
	FText MarkdownHighlightText;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Markdown")
	TEnumAsByte<ETextJustify::Type> MarkdownTextJustification = ETextJustify::Left;

	// Methods
	UFUNCTION(BlueprintCallable, Category = "Markdown")
	void SetNewText(const FText& InNewText);

private:
	static FText ProcessMarkdownAttribute(const FText& RawMarkdown);
	static FString ProcessInlineFormatting(const FString& InputText, const FString& BlockTag, bool bUseBlockTag = true,
	                                       bool bParentBold = false,
	                                       bool bParentItalic = false);
};
